import { useHotkeys } from 'react-hotkeys-hook'
import { ZOOM_CONSTRAINTS } from '../constants'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import { RefCallback } from 'react'

/**
 * A custom hook that sets up keyboard shortcuts for timeline controls
 *
 * Keyboard shortcuts:
 * - Space: Play/Pause
 * - Cmd/Ctrl + Z: Undo
 * - Cmd/Ctrl + Shift + Z or Cmd/Ctrl + Y: Redo
 * - Alt + Plus/=: Zoom in
 * - Alt + Minus/-: Zoom out
 */
export const useTimelineShortcuts = (): Array<RefCallback<any>> => {
  const {
    history: { canUndo, canRedo, undo, redo },
    videoPlayer: { togglePlayPause, currentFrame, seekTo }
  } = useEditorContext()

  const { zoomScale, setZoomScale } = useTimeline()

  return [
    useHotkeys(
      'space',
      () => {
        togglePlayPause()
      },
      { preventDefault: true, enableOnFormTags: false },
    ),

    useHotkeys(
      'meta+z, ctrl+z',
      () => {
        if (canUndo) undo()
      },
      { preventDefault: true }
    ),

    useHotkeys(
      'meta+shift+z, ctrl+shift+z, meta+y, ctrl+y',
      () => {
        if (canRedo) redo()
      },
      { preventDefault: true }
    ),

    useHotkeys(
      'alt+=, alt+plus',
      () => {
        setZoomScale(zoomScale + ZOOM_CONSTRAINTS.step)
      },
      { preventDefault: true }
    ),

    useHotkeys(
      'alt+-, alt+minus',
      () => {
        setZoomScale(zoomScale - ZOOM_CONSTRAINTS.step)
      },
      {
        keydown: true,
        preventDefault: true,
      },
    ),

    useHotkeys(
      'ArrowRight',
      () => {
        seekTo(currentFrame + 1)
      },
      { preventDefault: true, enableOnFormTags: false },
    ),

    useHotkeys(
      'ArrowLeft',
      () => {
        seekTo(currentFrame - 1)
      },
      { preventDefault: true, enableOnFormTags: false },
    ),
  ]
}
