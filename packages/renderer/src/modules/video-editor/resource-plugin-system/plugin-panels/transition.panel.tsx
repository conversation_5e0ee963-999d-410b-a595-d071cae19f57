import React, { memo, useCallback, useMemo } from 'react'
import { TransitionType } from '@app/shared/types/overlay'
import { ResourcePanelLayout, ResourceTab } from '../components/resource-panel-layout'
import { ResourcePlugins, ResourceTabType } from '@/modules/video-editor/resource-plugin-system'
import { EditorDraggableTypes, useTypedDraggable } from '@/modules/video-editor/components/editor-dnd-wrapper'
import { cn } from '@/components/lib/utils'

// 转场效果数据
const TRANSITION_EFFECTS = [
  {
    id: TransitionType.DISSOLVE,
    name: '叠化',
    type: TransitionType.DISSOLVE,
    description: '两个画面逐渐融合过渡'
  },
  {
    id: TransitionType.SLIDE,
    name: '滑动',
    type: TransitionType.SLIDE,
    description: '画面向指定方向滑动切换'
  },
  {
    id: TransitionType.WIPE,
    name: '擦除',
    type: TransitionType.WIPE,
    description: '画面向指定方向擦除切换'
  },
  {
    id: TransitionType.TEAR,
    name: '撕扯',
    type: TransitionType.TEAR,
    description: '画面向指定方向撕扯切换'
  },
  {
    id: TransitionType.BLOOM,
    name: '泛光',
    type: TransitionType.BLOOM,
    description: '通过光效过渡切换画面'
  },
  {
    id: TransitionType.BLUR,
    name: '模糊',
    type: TransitionType.BLUR,
    description: '通过模糊效果过渡切换'
  },
  {
    id: TransitionType.FLASH,
    name: '闪光',
    type: TransitionType.FLASH,
    description: '通过闪光效果过渡切换'
  },
  {
    id: TransitionType.ZOOM_OUT,
    name: '推远',
    type: TransitionType.ZOOM_OUT,
    description: '画面推远过渡切换'
  },
  {
    id: TransitionType.ZOOM_IN,
    name: '推近',
    type: TransitionType.ZOOM_IN,
    description: '画面推近过渡切换'
  }
]

interface TransitionItemProps {
  transition: typeof TRANSITION_EFFECTS[0]
}

/**
 * 转场效果卡片组件
 */
const TransitionItemContent: React.FC<TransitionItemProps> = memo(({ transition }) => {
  return (
    <div className="aspect-square" draggable={false}>
      <div
        className={cn(
          `group relative w-full h-full rounded border
           bg-gray-800/40 border-gray-700/10
           hover:border-blue-500/20 hover:bg-blue-500/5
           transition-all overflow-hidden cursor-grab`
        )}
      >
        {/* 转场效果示意图区域 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-16 h-16 bg-gradient-to-r from-gray-600 to-gray-400 rounded flex items-center justify-center">
            <span className="text-white text-xs font-medium">
              {transition.name}
            </span>
          </div>
        </div>

        {/* 转场效果名称 */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-2">
          <div className="font-medium">{transition.name}</div>
        </div>
      </div>
    </div>
  )
})

/**
 * 可拖拽的转场效果组件
 */
const TransitionItem: React.FC<TransitionItemProps> = ({ transition }) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.Resource,
    transition.id,
    {
      sourcePlugin: ResourcePlugins.TRANSITION,
      data: transition
    }
  )

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      <TransitionItemContent transition={transition} />
    </div>
  )
}

const TransitionResourceTabs: ResourceTab[] = [
  {
    label: '转场效果',
    value: ResourceTabType.ONLINE,
    showSearch: false,
    showCategorySelector: false,
    searchPlaceholder: ''
  }
]

/**
 * 转场效果面板组件
 * 显示各种转场效果，并允许用户将转场效果拖拽到时间轴
 */
function TransitionPanel() {
  const renderTransitionContent = useCallback(() => (
    <div className="grid grid-cols-3 gap-3 pt-3 pb-3">
      {TRANSITION_EFFECTS.map(transition => (
        <TransitionItem
          key={transition.id}
          transition={transition}
        />
      ))}
    </div>
  ), [])

  // 配置标签页内容
  const tabs = useMemo(() => {
    return TransitionResourceTabs.map(tab => ({
      ...tab,
      renderContent: () => renderTransitionContent(),
      isEmpty: false,
      emptyText: '暂无转场效果',
    }))
  }, [renderTransitionContent])

  return (
    <ResourcePanelLayout
      tabs={tabs}
      defaultTab={ResourceTabType.ONLINE}
      categories={[]}
      selectedCategory=""
      onCategoryChange={() => {}}
      searchKey=""
      onSearchChange={() => {}}
    />
  )
}

export default memo(TransitionPanel)
