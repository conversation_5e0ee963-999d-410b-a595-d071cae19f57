import React, { Fragment, useMemo } from 'react'
import { AbsoluteFill, Sequence } from 'remotion'
import { LayerContent } from './layer-content'
import { OverlayType, RenderableOverlay } from '@app/shared/types/overlay'

/**
 * Props for the Layer component
 * @property {Overlay} overlay - The overlay object containing position, dimensions, and content information
 * @property {string | undefined} baseUrl - The base URL for the video
 */
export const Layer: React.FC<{
  overlay: RenderableOverlay
  baseUrl?: string
}> = ({ overlay }) => {
  if (overlay.type === OverlayType.STORYBOARD) return null
  if (overlay.durationInFrames <= 0) return null

  /**
   * Memoized style calculations for the layer
   * Handles positioning, dimensions, rotation, and z-index based on:
   * - Overlay position (left, top)
   * - Dimensions (width, height)
   * - Rotation
   * - Row position for z-index stacking
   * - Selection state for pointer events
   *
   * @returns {React.CSSProperties} Computed styles for the layer
   */
  const style: React.CSSProperties = useMemo(() => {
    return {
      position: 'absolute',
      left: overlay.left,
      top: overlay.top,
      width: overlay.width,
      height: overlay.height,
      transform: `rotate(${overlay.rotation || 0}deg)`,
      transformOrigin: 'center center',
      zIndex: overlay.zIndex,
    }
  }, [overlay])

  if (overlay.type === 'sound') {
    return (
      <Sequence
        key={overlay.id}
        from={overlay.from}
        durationInFrames={overlay.durationInFrames}
      >
        <LayerContent overlay={overlay} />
      </Sequence>
    )
  }

  const { from, durationInFrames } = useMemo(() => {
    const { from, durationInFrames, type } = overlay
    if (type !== OverlayType.VIDEO) {
      return { from, durationInFrames }
    }

    const { leadingTransition, trailingTransition } = overlay
    const leadingDuration = (leadingTransition?.durationInFrames ?? 0) / 2
    const trailingDuration = (trailingTransition?.durationInFrames ?? 0) / 2

    return {
      from: from - leadingDuration,
      durationInFrames: durationInFrames + leadingDuration + trailingDuration
    }
  }, [overlay])

  return (
    <Fragment>
      <AbsoluteFill
        style={{
          overflow: 'hidden',
          maxWidth: '3000px',
        }}
      >
        <Sequence
          key={overlay.id}
          from={from}
          durationInFrames={durationInFrames}
          layout="none"
        >
          <div style={style} data-overlay-id={overlay.id}>
            <LayerContent overlay={overlay} />
          </div>
        </Sequence>
      </AbsoluteFill>
    </Fragment>
  )
}
