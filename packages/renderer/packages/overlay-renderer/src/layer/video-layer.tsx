import { continueRender, delayRender, Video } from 'remotion'
import { RenderableOverlay, VideoOverlay } from '@app/shared/types/overlay'
import React, { useEffect, useMemo, useRef } from 'react'
import { getProgressiveOverlayProps } from '../utils/getProgressiveOverlayProps'
import { useRenderContext } from '../render.context'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import * as THREE from 'three'
import { useFrame } from '@react-three/fiber'

// 基础顶点着色器
const VERTEX_SHADER = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

// 基础片段着色器 - 只负责显示视频纹理
const FRAGMENT_SHADER = /* glsl */ `
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D u_tex;
  uniform float u_opacity;

  void main() {
    vec4 color = texture2D(u_tex, vUv);
    gl_FragColor = vec4(color.rgb, color.a * u_opacity);
  }
`

// 全屏 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay
}> = ({ video, overlay }) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  const texture = useVideoTexture(video)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // 配置纹理属性
  useMemo(() => {
    if (!texture) return
    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  // 计算视频变换参数
  const { scale, position, rotation } = useMemo(() => {
    const videoAspectRatio = overlay.originalMeta.width / overlay.originalMeta.height

    // 在 ThreeCanvas 中，当设置了具体的 width 和 height 时，
    // 坐标系会被映射到像素空间，而不是标准化的 [-1, 1] 空间

    // 计算 overlay 矩形的中心点（像素坐标）
    const centerX = overlay.left + overlay.width / 2
    const centerY = overlay.top + overlay.height / 2

    // 将像素坐标转换为 Three.js 坐标系
    // Three.js 坐标原点在屏幕中心，需要进行偏移
    const positionX = centerX - playerWidth / 2
    const positionY = -(centerY - playerHeight / 2)  // Y轴翻转

    // 计算缩放
    // planeGeometry args=[1, 1/aspectRatio] 创建的平面：
    // - 宽度: 1 个单位
    // - 高度: 1/aspectRatio 个单位
    // 需要缩放到 overlay 的像素尺寸

    const scaleX = overlay.width  // 直接使用像素宽度
    const scaleY = overlay.height // 直接使用像素高度

    // 但是需要考虑 planeGeometry 的实际高度
    // 如果视频是横向的（aspectRatio > 1），planeHeight < 1
    // 如果视频是纵向的（aspectRatio < 1），planeHeight > 1
    const planeHeight = 1 / videoAspectRatio
    const adjustedScaleY = scaleY / planeHeight

    // 旋转角度转换为弧度
    const rotationZ = (overlay.rotation || 0) * Math.PI / 180

    return {
      scale: [scaleX, adjustedScaleY, 1] as [number, number, number],
      position: [positionX, positionY, 0] as [number, number, number],
      rotation: [0, 0, rotationZ] as [number, number, number]
    }
  }, [overlay, playerWidth, playerHeight])

  // 计算视频宽高比
  const aspectRatio = useMemo(() => {
    return overlay.originalMeta.width / overlay.originalMeta.height
  }, [overlay.originalMeta])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_opacity: { value: overlay.styles.opacity ?? 1 },
    }),
    [texture, overlay.styles.opacity]
  )

  // 每帧更新透明度
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_opacity.value = overlay.styles.opacity ?? 1
    }
  })

  return (
    <mesh
      scale={scale}
      position={position}
      rotation={rotation}
    >
      <planeGeometry args={[1, 1 / aspectRatio]} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          vertexShader={VERTEX_SHADER}
          fragmentShader={FRAGMENT_SHADER}
          uniforms={uniforms}
          transparent={true}
          toneMapped={false}
        />
      )}
    </mesh>
  )
}

interface VideoLayerProps {
  overlay: RenderableOverlay & VideoOverlay
}

export const VideoLayer: React.FC<VideoLayerProps> = ({ overlay }) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const src = overlay.localSrc || overlay.src

  useEffect(() => {
    const handle = delayRender('Loading video')

    const handleLoadedMetadata = () => {
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    videoRef.current?.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.current?.addEventListener('error', handleError)

    return () => {
      videoRef.current?.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoRef.current?.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [src])

  return (
    <div
      id={`video-layer-${overlay.id}`}
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        width: playerWidth,
        height: playerHeight,
        pointerEvents: 'none'
      }}
    >
      <ThreeCanvas
        orthographic={false}
        width={playerWidth}
        height={playerHeight}
        camera={{ position: [0, 0, 1] }}
        style={{ width: '100%', height: '100%' }}
      >
        <VideoPlane
          video={videoRef}
          overlay={overlay}
        />
      </ThreeCanvas>

      {/* 隐藏的视频元素作为纹理源 */}
      <Video
        ref={videoRef}
        src={src}
        style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
        volume={overlay.styles.volume ?? 1}
        {...getProgressiveOverlayProps(overlay)}
      />
    </div>
  )
}
