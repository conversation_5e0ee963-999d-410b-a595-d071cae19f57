import { continueRender, delayRender, Video } from 'remotion'
import { RenderableOverlay, VideoOverlay } from '@app/shared/types/overlay'
import React, { useEffect, useMemo, useRef } from 'react'
import { getProgressiveOverlayProps } from '../utils/getProgressiveOverlayProps'
import { useRenderContext } from '../render.context'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import * as THREE from 'three'
import { useFrame } from '@react-three/fiber'

// 基础顶点着色器
const VERTEX_SHADER = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

// 基础片段着色器 - 只负责显示视频纹理
const FRAGMENT_SHADER = /* glsl */ `
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D u_tex;
  uniform float u_opacity;

  void main() {
    vec4 color = texture2D(u_tex, vUv);
    gl_FragColor = vec4(color.rgb, color.a * u_opacity);
  }
`

// 全屏 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay
}> = ({ video, overlay }) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  const texture = useVideoTexture(video)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // 配置纹理属性
  useMemo(() => {
    if (!texture) return
    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  // 计算视频变换参数
  const { scale, position, rotation } = useMemo(() => {
    const videoAspectRatio = overlay.originalMeta.width / overlay.originalMeta.height

    // 计算缩放：将 overlay 像素尺寸转换为 Three.js 标准化坐标
    // Three.js 坐标系范围是 [-1, 1]，总宽度/高度为 2
    const scaleX = overlay.width / overlay.originalMeta.width
    const scaleY = overlay.height / overlay.originalMeta.width

    // 计算位置：将 overlay 像素坐标转换为 Three.js 坐标
    // overlay.left/top 是相对于播放器左上角的像素坐标
    // 需要转换为以中心为原点的坐标系
    const positionX = (overlay.left + overlay.width / 2) / playerWidth * 2 - 1
    const positionY = -((overlay.top + overlay.height / 2) / playerHeight * 2 - 1) // Y轴翻转

    // 旋转角度转换为弧度
    const rotationZ = -(overlay.rotation || 0) * Math.PI / 180

    return {
      scale: [scaleX, scaleY, 1] as [number, number, number],
      position: [positionX, positionY, 0] as [number, number, number],
      rotation: [0, 0, rotationZ] as [number, number, number]
    }
  }, [overlay, playerWidth, playerHeight])

  // 计算视频宽高比
  const aspectRatio = useMemo(() => {
    return overlay.originalMeta.width / overlay.originalMeta.height
  }, [overlay.originalMeta])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_opacity: { value: overlay.styles.opacity ?? 1 },
    }),
    [texture, overlay.styles.opacity]
  )

  // 每帧更新透明度
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_opacity.value = overlay.styles.opacity ?? 1
    }
  })

  return (
    <mesh
      scale={scale}
      position={position}
      rotation={rotation}
    >
      <planeGeometry args={[1, 1 / aspectRatio]} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          vertexShader={VERTEX_SHADER}
          fragmentShader={FRAGMENT_SHADER}
          uniforms={uniforms}
          transparent={true}
          toneMapped={false}
        />
      )}
    </mesh>
  )
}

interface VideoLayerProps {
  overlay: RenderableOverlay & VideoOverlay
}

export const VideoLayer: React.FC<VideoLayerProps> = ({ overlay }) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const src = overlay.localSrc || overlay.src

  useEffect(() => {
    const handle = delayRender('Loading video')

    const handleLoadedMetadata = () => {
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    videoRef.current?.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.current?.addEventListener('error', handleError)

    return () => {
      videoRef.current?.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoRef.current?.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [src])

  return (
    <div
      id={`video-layer-${overlay.id}`}
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        width: playerWidth,
        height: playerHeight,
        pointerEvents: 'none'
      }}
    >
      <ThreeCanvas
        orthographic={false}
        width={playerWidth}
        height={playerHeight}
        camera={{ position: [0, 0, 1] }}
        style={{ width: '100%', height: '100%' }}
      >
        <VideoPlane
          video={videoRef}
          overlay={overlay}
        />
      </ThreeCanvas>

      {/* 隐藏的视频元素作为纹理源 */}
      <Video
        ref={videoRef}
        src={src}
        style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
        volume={overlay.styles.volume ?? 1}
        {...getProgressiveOverlayProps(overlay)}
      />
    </div>
  )
}
