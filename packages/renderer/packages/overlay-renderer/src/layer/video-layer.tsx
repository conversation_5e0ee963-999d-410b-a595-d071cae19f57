import { continueRender, delayRender, Video } from 'remotion'
import { RenderableOverlay, VideoOverlay } from '@app/shared/types/overlay'
import React, { useEffect, useMemo, useRef } from 'react'
import { getProgressiveOverlayProps } from '../utils/getProgressiveOverlayProps'
import { useRenderContext } from '../render.context'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import * as THREE from 'three'
import { useFrame, useThree } from '@react-three/fiber'

// 基础顶点着色器
const VERTEX_SHADER = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

// 基础片段着色器 - 只负责显示视频纹理
const FRAGMENT_SHADER = /* glsl */ `
  precision highp float;
  varying vec2 vUv;

  uniform sampler2D u_tex;
  uniform float u_opacity;

  void main() {
    vec4 color = texture2D(u_tex, vUv);
    gl_FragColor = vec4(color.rgb, color.a * u_opacity);
  }
`

interface VideoLayerProps {
  overlay: RenderableOverlay & VideoOverlay
}

// 全屏 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay
}> = ({ video, overlay }) => {
  const { viewport: vp } = useThree()
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  const texture = useVideoTexture(video)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // 配置纹理属性
  useMemo(() => {
    if (!texture) return
    // texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    // texture.minFilter = THREE.LinearFilter
    // texture.magFilter = THREE.LinearFilter
    // texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  // 计算视频变换参数
  const { rotation } = useMemo(() => {
    // 旋转角度转换为弧度
    const rotationZ = -(overlay.rotation || 0) * Math.PI / 180

    return {
      position: [0, 0, 0] as [number, number, number],
      rotation: [0, 0, rotationZ] as [number, number, number]
    }
  }, [overlay, playerWidth, playerHeight])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_opacity: { value: overlay.styles.opacity ?? 1 },
    }),
    [texture, overlay.styles.opacity]
  )

  // 每帧更新透明度
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_opacity.value = overlay.styles.opacity ?? 1
    }
  })

  const planeW = overlay.width / playerWidth
  const planeH = overlay.height / playerHeight

  console.log({  playerWidth, playerHeight, planeW, planeH })

  return (
    <mesh
      rotation={rotation}
    >
      <planeGeometry args={[overlay.width / vp.factor, overlay.height / vp.factor]} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          vertexShader={VERTEX_SHADER}
          fragmentShader={FRAGMENT_SHADER}
          uniforms={uniforms}
          transparent={true}
          toneMapped={false}
        />
      )}
      {/*{texture && (*/}
      {/*  <meshBasicMaterial*/}
      {/*    map={texture}*/}
      {/*  />*/}
      {/*)}*/}
    </mesh>
  )
}

export const VideoLayer: React.FC<VideoLayerProps> = ({ overlay }) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const src = overlay.localSrc || overlay.src

  useEffect(() => {
    const handle = delayRender('Loading video')

    const handleLoadedMetadata = () => {
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    videoRef.current?.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.current?.addEventListener('error', handleError)

    return () => {
      videoRef.current?.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoRef.current?.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [src])

  return (
    <div
      id={`video-layer-${overlay.id}`}
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        width: playerWidth,
        height: playerHeight,
        pointerEvents: 'none'
      }}
    >
      <ThreeCanvas
        width={playerWidth}
        height={playerHeight}
        dpr={1}
        camera={{ position: [0, 0, 1] }}
        style={{ width: '100%', height: '100%', border: '4px solid red' }}
      >
        {/*<OrthographicCamera makeDefault position={[0, 0, 10]} zoom={100} />*/}
        <VideoPlane video={videoRef} overlay={overlay} />
      </ThreeCanvas>

      {/* 隐藏的视频元素作为纹理源 */}
      <Video
        ref={videoRef}
        src={src}
        style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
        volume={overlay.styles.volume ?? 1}
        {...getProgressiveOverlayProps(overlay)}
      />
    </div>
  )
}
