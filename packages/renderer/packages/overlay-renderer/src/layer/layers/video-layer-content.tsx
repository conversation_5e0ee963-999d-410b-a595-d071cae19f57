import { continueRender, delayRender, Video } from 'remotion'
import { VideoOverlay } from '@app/shared/types/overlay'
import React, { useEffect, useMemo, useRef } from 'react'
import { getProgressiveOverlayProps } from '../../utils/getProgressiveOverlayProps'
import { useRenderContext } from '../../render.context'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import * as THREE from 'three'
import { useFrame } from '@react-three/fiber'

// 基础顶点着色器
const VERTEX_SHADER = /* glsl */ `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`

// 基础片段着色器 - 只负责显示视频纹理
const FRAGMENT_SHADER = /* glsl */ `
  precision highp float;
  varying vec2 vUv;
  uniform sampler2D u_tex;
  uniform float u_opacity;

  void main() {
    vec4 color = texture2D(u_tex, vUv);
    gl_FragColor = vec4(color.rgb, color.a * u_opacity);
  }
`

interface VideoLayerContentProps {
  overlay: VideoOverlay
  inTransition?: boolean
}

// 基础的 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay
  opacity: number
}> = ({ video, overlay, opacity }) => {
  const texture = useVideoTexture(video)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // 配置纹理属性
  useMemo(() => {
    if (!texture) return
    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  // 计算视频宽高比
  const aspectRatio = useMemo(() => {
    return overlay.originalMeta.width / overlay.originalMeta.height
  }, [overlay.originalMeta])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_opacity: { value: opacity },
    }),
    [texture, opacity]
  )

  // 每帧更新透明度
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_opacity.value = opacity
    }
  })

  return (
    <mesh>
      <planeGeometry args={[1, 1 / aspectRatio]} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          vertexShader={VERTEX_SHADER}
          fragmentShader={FRAGMENT_SHADER}
          uniforms={uniforms}
          transparent={true}
          toneMapped={false}
        />
      )}
    </mesh>
  )
}

export const VideoLayerContent: React.FC<VideoLayerContentProps> = ({ overlay, inTransition }) => {
  const { playerMetadata: { width, height } } = useRenderContext()
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const src = overlay.localSrc || overlay.src

  useEffect(() => {
    const handle = delayRender('Loading video')

    const video = document.createElement('video')
    video.src = src

    const handleLoadedMetadata = () => {
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('error', handleError)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [src])

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: overlay.styles.paddingBackgroundColor || 'transparent',
    opacity: overlay.styles.opacity ?? 1,
  }

  const baseTransform = overlay.styles.transform || 'none'
  const padding = overlay.styles.padding || 0
  const paddingContainerStyle: React.CSSProperties = {
    position: 'relative',
    width: `${100 - padding}%`,
    height: `${100 - padding}%`,
    overflow: 'hidden',
    transform: baseTransform
  }

  return (
    <div style={containerStyle} className="video-layer-content-container">
      <div style={paddingContainerStyle}>
        <ThreeCanvas
          orthographic={false}
          width={width}
          height={height}
          camera={{ position: [0, 0, 1] }}
          style={{ width: '100%', height: '100%' }}
        >
          <VideoPlane
            video={videoRef}
            overlay={overlay}
            opacity={1}
          />
        </ThreeCanvas>

        <Video
          ref={videoRef}
          src={src}
          style={{ position: 'absolute', opacity: 0 }}
          volume={overlay.styles.volume ?? 1}
          {...getProgressiveOverlayProps(overlay)}
        />
      </div>
    </div>
  )
}
