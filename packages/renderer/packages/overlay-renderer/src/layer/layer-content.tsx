import React from 'react'

import {
  Capt<PERSON><PERSON><PERSON>er<PERSON>ontent,
  SoundLayerContent,
  StickerLayerContent,
  TextLayerContent,
  VideoLayerContent
} from './layers'

import { Overlay, OverlayType } from '@app/shared/types/overlay'

interface LayerContentProps {
  overlay: Overlay
}

const COMMON_STYLE: React.CSSProperties = {
  width: '100%',
  height: '100%',
}

export const LayerContent: React.FC<LayerContentProps> = ({ overlay }) => {
  switch (overlay.type) {
    case OverlayType.VIDEO:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <VideoLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.SOUND:
      return <SoundLayerContent overlay={overlay} />

    case OverlayType.TEXT:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <TextLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.CAPTION:
      return (
        <div
          style={{
            ...COMMON_STYLE,
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
          }}
        >
          <CaptionLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.STICKER:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <StickerLayerContent overlay={overlay} />
        </div>
      )

      // case OverlayType.TRANSITION:
      //   return (
      //     <div style={{ ...COMMON_STYLE }}>
      //       <TransitionLayerContent overlay={overlay} />
      //     </div>
      //   )

    default:
      return null
  }
}
